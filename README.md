# Student Programming Hub – Modern HTML Crash Course

This is a modern educational website for students to learn HTML, programming, and web development. Built with Vite, TypeScript, and Supabase for secure authentication and a fast, beautiful user experience.

## Features

- Modern responsive design (dark/light mode, glassmorphism, animations)
- HTML, CSS, and programming crash course content
- Supabase authentication (Google, GitHub, Email/Password)
- Secure environment variable management
- Testimonials, resources, and community links

## Getting Started

1. **Clone the repository**
2. **Install dependencies**

   ```sh
   cd .vanilla-ts
   npm install
   ```

3. **Set up environment variables**

   Create a `.env` file in `.vanilla-ts`:

   ```env
   VITE_SUPABASE_URL=your-supabase-url
   VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

4. **Run the development server**

   ```sh
   npm run dev
   ```

5. **Build for production**

   ```sh
   npm run build
   ```

## Folder Structure

- `index.html` – Main entry point
- `src/` – Source files (TypeScript, CSS)
- `public/` – Static assets (images, etc.)
- `.env` – Environment variables (never commit this!)

## Security

- Never commit your `.env` file or Supabase keys to version control.
- All environment variables must use the `VITE_` prefix for frontend access.

## License

MIT
