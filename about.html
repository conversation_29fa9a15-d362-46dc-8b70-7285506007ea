<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us | Student Programming Hub</title>
    <meta name="description" content="About Student Programming Hub: our mission, vision, values, team, and free resources for learning programming and web development.">
    <meta name="keywords" content="About, HTML, programming, education, students, web development, resources, tutorials, community, success stories">
    <meta name="author" content="Student Programming Hub">
    <script>
      // Prevent flash of wrong theme
      try {
        var theme = localStorage.getItem('theme');
        if (theme === 'light') {
          document.documentElement.classList.add('light-theme');
        }
      } catch(e) {}
    </script>
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="/src/styles/modules/glass-card.module.css">
    <link rel="stylesheet" href="/src/styles/modules/animations.module.css">
    <script type="module" src="/scripts/main.ts"></script>
</head>
<body>
    <header>
      <div class="toggle-theme" id="toggleTheme" title="Toggle light/dark mode">
        <svg id="themeIcon" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="5"/>
          <g id="rays">
            <line x1="12" y1="1" x2="12" y2="3"/>
            <line x1="12" y1="21" x2="12" y2="23"/>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
            <line x1="1" y1="12" x2="3" y2="12"/>
            <line x1="21" y1="12" x2="23" y2="12"/>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
          </g>
        </svg>
      </div>
      <nav aria-label="Main navigation">
        <a href="/index.html">Home</a>
        <a href="/services.html">Services</a>
        <a href="/Blog.html">Blog</a>
        <a href="/about.html" aria-current="page">About</a>
        <a href="/Privacy.html">Privacy Policy</a>
        <a href="/terms and conditions.html">Terms and Conditions</a>
        <button id="signOutBtn" style="margin-left:1em;">Sign Out</button>
      </nav>
    </header>
    <main>
      <div class="container">
        <section class="card glass-card fade-in flex-row">
          <div class="flex-col" style="flex:2">
            <h1>About Us</h1>
            <p>This is a website for students to learn about programming and other things related to programming, as well as to help them find jobs, internships, mentors, and projects to work on. We also help students connect with each other.</p>
          </div>
          <img src="/pexels-divinetechygirl-1181316.jpg" alt="About our team" width="300" height="200" class="img-card" style="flex:1;max-width:300px;">
        </section>
        <section class="card glass-card fade-in flex-row">
          <img src="/pexels-divinetechygirl-1181334.jpg" alt="Learning together" width="300" height="200" class="img-card" style="flex:1;max-width:300px;">
          <div class="flex-col" style="flex:2">
            <h2>Our Mission</h2>
            <p>Our mission is to provide a platform for students to learn about programming and related topics, and to help them find jobs, internships, mentors, and projects to work on.</p>
          </div>
        </section>
        <section class="card glass-card fade-in flex-row">
          <div class="flex-col" style="flex:2">
            <h3>Our Vision</h3>
            <p>Our vision is to be the best website for students to learn about programming and related topics, and to help them find opportunities and connections.</p>
          </div>
          <img src="/pexels-harold-vasquez-853421-2653362.jpg" alt="Vision" width="300" height="200" class="img-card" style="flex:1;max-width:300px;">
        </section>
        <section class="card glass-card fade-in">
          <h4>Our Values</h4>
          <ul>
            <li>Inclusivity and diversity</li>
            <li>Support and mentorship</li>
            <li>Collaboration and teamwork</li>
            <li>Continuous learning</li>
          </ul>
        </section>
        <section class="card glass-card fade-in flex-row">
          <img src="/pexels-mart-production-8217488.jpg" alt="Teamwork" width="300" height="200" class="img-card" style="flex:1;max-width:300px;">
          <div class="flex-col" style="flex:2">
            <h5>Our Team</h5>
            <p>Our team is made up of students who are passionate about programming and helping others succeed in tech.</p>
          </div>
        </section>
        <section class="card glass-card fade-in">
          <h6>Our Founder</h6>
          <p>Our founder is a student who is passionate about programming and building a supportive community for learners.</p>
        </section>
        <section class="card glass-card fade-in">
          <h4>Free Resources</h4>
          <ul>
            <li><a href="memoir.pdf free resources to.pdf" download="memoir.pdf">Free resources to learn programming 💻😊 (PDF)</a></li>
            <li><a href="https://www.freecodecamp.org/" target="_blank">FreeCodeCamp</a></li>
            <li><a href="https://www.w3schools.com/" target="_blank">W3Schools</a></li>
            <li><a href="https://developer.mozilla.org/" target="_blank">MDN Web Docs</a></li>
          </ul>
        </section>
        <section class="testimonials glass-card fade-in">
          <div class="testimonial">
            "I found my first internship through this community!"<br><b>- Sarah, Student</b>
          </div>
          <div class="testimonial">
            "The resources and mentorship helped me land my dream job."<br><b>- James, Graduate</b>
          </div>
        </section>
        <div class="cta glass-card fade-in">
          <p>Want to join our community or get updates? <button class="cta-btn">Sign up for our newsletter!</button></p>
        </div>
      </div>
    </main>
    <footer>
      &copy; 2025 All rights reserved | Student Programming Hub
    </footer>
</body>
</html>