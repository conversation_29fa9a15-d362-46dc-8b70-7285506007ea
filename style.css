/* style.css - Modern, clean, responsive styles for the student education website */

/* === MODERN DARK THEME & GLASSMORPHISM === */
:root {
  --primary: #2d3e50;
  --accent: #ffb347;
  --bg: #0a0a0a;
  --card-bg: rgba(255,255,255,0.05);
  --glass-blur: blur(10px);
  --text: #fff;
  --white: #fff;
  --neon-green: #0ff;
  --purple: #a855f7;
  --poppins: 'Poppins', 'Segoe UI', Arial, sans-serif;
}

body {
  font-family: var(--poppins);
  background: var(--bg);
  color: var(--text);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

nav {
  background: var(--primary);
  padding: 1rem 0;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  transition: background 0.3s;
}
nav a {
  color: var(--white);
  text-decoration: none;
  margin: 0 1.5rem;
  font-weight: 500;
  font-size: 1.1rem;
  transition: color 0.2s;
  position: relative;
}
nav a:hover {
  color: var(--neon-green);
}
nav a::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: 0;
  left: 0;
  background: var(--neon-green);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}
nav a:hover::before {
  transform: scaleX(1);
}

.container {
  max-width: 1100px;
  margin: 2rem auto;
  background: transparent;
  border-radius: 20px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  padding: 2.5rem 3rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Glassmorphism for cards/sections */
.card, .container, .auth-forms, .testimonials, .cta, .contact-info {
  background: var(--card-bg);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: 16px;
  border: 1px solid rgba(255,255,255,0.18);
  margin-bottom: 2rem;
}

.flex-row {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  align-items: flex-start;
  justify-content: space-between;
}
.flex-col {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.card, .feature-card {
  background: var(--card-bg);
  backdrop-filter: var(--glass-blur);
  border-radius: 20px;
  padding: 2rem;
  margin: 2rem 0;
  border: 1px solid rgba(255,255,255,0.1);
  box-shadow: 0 1px 8px rgba(0,0,0,0.10);
  transition: transform 0.3s, box-shadow 0.2s;
}
.card:hover, .feature-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 4px 24px var(--purple);
}

h1, h2, h3, h4, h5, h6 {
  color: var(--neon-green);
  text-shadow: 0 0 8px var(--neon-green), 0 0 16px var(--purple);
  font-family: var(--poppins);
  letter-spacing: 1px;
}

h1 { font-size: 3em; }
h2 { font-size: 2.2em; }
h3 { font-size: 1.7em; }
h4 { font-size: 1.3em; }
h5 { font-size: 1.1em; }
h6 { font-size: 1em; }

/* Button and form styling */
button, .cta-btn {
  border-radius: 8px;
  border: none;
  padding: 0.7em 1.5em;
  font-size: 1.1em;
  font-weight: 600;
  font-family: inherit;
  background: linear-gradient(90deg, var(--neon-green), var(--purple));
  color: #111;
  box-shadow: 0 0 8px var(--neon-green), 0 0 16px var(--purple);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}
button:hover, .cta-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 0 16px var(--neon-green), 0 0 32px var(--purple);
}

input[type="email"], input[type="password"] {
  border-radius: 6px;
  border: 1px solid #444;
  padding: 0.7em 1em;
  font-size: 1em;
  margin-bottom: 1em;
  background: rgba(255,255,255,0.1);
  color: var(--text);
  width: 100%;
  box-sizing: border-box;
}
input:focus {
  outline: 2px solid var(--neon-green);
  background: rgba(0,255,255,0.08);
}

.auth-forms {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
  margin-top: 2rem;
}
.auth-forms form {
  background: var(--card-bg);
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(31, 38, 135, 0.17);
  min-width: 300px;
  max-width: 350px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: fadeIn 1s;
}

.auth-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}
.auth-form {
  margin: 0 auto;
  max-width: 350px;
  width: 100%;
  box-sizing: border-box;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(40px); }
  to { opacity: 1; transform: translateY(0); }
}
.card, .auth-forms form, .cta, .testimonials, .contact-info {
  animation: fadeIn 1s;
}

.cta, .cta-btn {
  background: linear-gradient(45deg, var(--neon-green), var(--purple));
  color: var(--dark-bg);
  text-shadow: 0 0 8px #fff8;
  box-shadow: 0 0 18px var(--neon-green);
  font-weight: 800;
  letter-spacing: 1px;
  border-radius: 50px;
  text-align: center;
  font-size: 1.2rem;
  margin: 2rem 0;
  border: none;
  padding: 1.2rem 2rem;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.2s;
}
.cta-btn:hover {
  transform: scale(1.05) rotate(3deg);
  box-shadow: 0 0 32px var(--purple);
}

img, .img-card {
  max-width: 100%;
  border-radius: 16px;
  margin: 1.2rem 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  object-fit: cover;
}

.testimonials {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  margin: 2rem 0;
}
.testimonial {
  background: var(--card-bg);
  border-left: 5px solid var(--neon-green);
  border-radius: 12px;
  padding: 1.2rem 1.5rem;
  box-shadow: 0 1px 6px rgba(0,0,0,0.04);
  flex: 1 1 300px;
  min-width: 220px;
  font-style: italic;
  position: relative;
  animation: fadeInUp 0.7s;
  font-size: 1.1rem;
  color: var(--white);
  text-shadow: 0 0 6px var(--neon-green);
}
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}
.fade-in {
  opacity: 0;
  animation: fadeIn 1.2s forwards;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.social-links {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin: 2rem 0;
}
.social-links a {
  color: var(--white);
  text-decoration: none;
  position: relative;
  padding: 0.5rem 1rem;
  font-size: 1.2rem;
}
.social-links a::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: 0;
  left: 0;
  background: var(--neon-green);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}
.social-links a:hover::before {
  transform: scaleX(1);
}

footer {
  background: var(--primary);
  color: var(--white);
  text-align: center;
  padding: 1.2rem 0 0.7rem 0;
  margin-top: 3rem;
  font-size: 1rem;
  letter-spacing: 0.5px;
}
address, .contact-info {
  font-style: normal;
  color: #aaa;
  margin-top: 1.5rem;
  font-size: 1rem;
}

/* === SUN/MOON THEME TOGGLE === */
.toggle-theme {
  position: fixed;
  top: 1.2rem;
  right: 2rem;
  z-index: 200;
  background: var(--card-bg);
  border: 1px solid rgba(255,255,255,0.15);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  transition: background 0.3s, box-shadow 0.3s;
}
.toggle-theme svg {
  width: 28px;
  height: 28px;
  fill: var(--neon-green);
  transition: fill 0.3s;
}
.toggle-theme.light svg {
  fill: #facc15;
}
body.light-theme {
  --bg: #f7f9fb;
  --text: #222;
  --primary: #fff;
  --card-bg: rgba(255,255,255,0.85);
  --white: #222;
}
body.light-theme nav {
  background: #fff;
  color: #222;
}
body.light-theme nav a {
  color: #222;
}
body.light-theme nav a:hover {
  color: var(--purple);
}
body.light-theme .card, body.light-theme .feature-card {
  background: rgba(255,255,255,0.85);
  color: #222;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
}
body.light-theme h1, body.light-theme h2, body.light-theme h3, body.light-theme h4, body.light-theme h5, body.light-theme h6 {
  color: #222;
}
body.light-theme .testimonial {
  color: #222;
  border-left: 5px solid var(--purple);
  text-shadow: none;
}
body.light-theme .cta, body.light-theme .cta-btn {
  color: #fff;
  background: linear-gradient(45deg, var(--purple), var(--neon-green));
}
body.light-theme .social-links a {
  color: #222;
}
body.light-theme address, body.light-theme .contact-info {
  color: #444;
}

@media (max-width: 900px) {
  .container {
    padding: 1.2rem 0.7rem;
  }
  .flex-row {
    flex-direction: column;
    gap: 1.5rem;
  }
}
@media (max-width: 600px) {
  nav {
    flex-direction: column;
    gap: 0.5rem;
  }
  .container {
    padding: 0.7rem 0.2rem;
  }
  .card, .feature-card {
    padding: 1rem 0.7rem;
  }
  .cta, .cta-btn {
    padding: 1rem 0.5rem;
    font-size: 1rem;
  }
  h1 {
    font-size: 2rem;
  }
  h2 {
    font-size: 1.3rem;
  }
}

/* Scroll to top button */
#upBtn {
  position: fixed;
  bottom: 2.5rem;
  right: 2.5rem;
  z-index: 150;
  background: linear-gradient(135deg, var(--neon-green), var(--purple));
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 54px;
  height: 54px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 24px var(--purple), 0 0 12px var(--neon-green);
  cursor: pointer;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s, transform 0.2s;
}
#upBtn svg {
  stroke: #fff;
  width: 28px;
  height: 28px;
}
#upBtn.show {
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0);
}
#upBtn:hover {
  transform: scale(1.1) translateY(-4px);
  box-shadow: 0 8px 32px var(--neon-green), 0 0 24px var(--purple);
}
@media (max-width: 600px) {
  #upBtn {
    bottom: 1.2rem;
    right: 1.2rem;
    width: 44px;
    height: 44px;
  }
  #upBtn svg {
    width: 22px;
    height: 22px;
  }
}

#newsletterForm {
  display: none;
}
#newsletterForm.open {
  display: flex;
  gap: 0.5em;
  align-items: center;
  justify-content: center;
}
