import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// This file is now empty because Supabase Auth UI React cannot be used in a non-React project.
// Please use your custom HTML forms and main.ts/auth.ts logic for authentication.
