// scripts/main.ts
// Modern TypeScript for interactivity and animation

import { signUp, signIn, resetPassword } from './auth';
import { supabase } from './supabaseClient';
import { z } from 'zod';
import DOMPurify from 'dompurify';

document.addEventListener('DOMContentLoaded', async () => {
  const loadingOverlay = document.getElementById('loadingOverlay');

  // --- AUTH REDIRECT LOGIC FOR MAIN CONTENT PAGES ---
  // Only run this on index.html and other protected pages, not on login.html
  const isLoginPage = window.location.pathname.endsWith('/login.html');
  if (!isLoginPage) {
    // Check if user is logged in
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      window.location.href = '/login.html';
      return;
    }
  }

  // If on login.html and already logged in, redirect to home
  if (isLoginPage) {
    const { data: { session } } = await supabase.auth.getSession();
    if (session) {
      window.location.href = '/index.html';
      return;
    }
  }

  // Navbar animation
  const nav = document.querySelector('nav');
  if (nav) {
    nav.classList.add('nav-animate');
  }

  // Simple fade-in for sections
  const sections = document.querySelectorAll('section, .cta, footer');
  sections.forEach((section, i) => {
    section.classList.add('fade-in');
    section.setAttribute('style', `animation-delay: ${0.2 + i * 0.15}s`);
  });

  // Newsletter CTA interaction (inline form)
  const showNewsletterFormBtn = document.getElementById('showNewsletterForm');
  const newsletterForm = document.getElementById('newsletterForm') as HTMLFormElement | null;
  const newsletterEmail = document.getElementById('newsletterEmail') as HTMLInputElement | null;
  const newsletterMsg = document.getElementById('newsletterMsg') as HTMLElement | null;

  if (showNewsletterFormBtn && newsletterForm) {
    showNewsletterFormBtn.addEventListener('click', (e) => {
      e.preventDefault();
      showNewsletterFormBtn.scrollIntoView({ behavior: 'auto', block: 'center' });
      // Toggle a class instead of display property
      newsletterForm.classList.toggle('open');
      if (newsletterForm.classList.contains('open')) {
        if (newsletterEmail) {
          newsletterEmail.focus();
        }
      }
      if (newsletterMsg) newsletterMsg.textContent = '';
    });
    newsletterForm.onsubmit = (e) => {
      e.preventDefault();
      if (!newsletterEmail) return;
      const email = newsletterEmail.value;
      const emailSchema = z.string().email({ message: 'Invalid email' });
      const result = emailSchema.safeParse(email);
      if (!result.success) {
        if (newsletterMsg) newsletterMsg.textContent = DOMPurify.sanitize(result.error.issues[0].message);
        return;
      }
      // Simulate success (in real app, send to backend)
      if (newsletterMsg) newsletterMsg.textContent = 'Thank you! Newsletter sign-up coming soon.';
      newsletterEmail.value = '';
    };
  }

  // Helper function for theme icon SVG
  function getThemeIconSVG(): string {
    return `<circle cx="12" cy="12" r="5"/><g id="rays"><line x1="12" y1="1" x2="12" y2="3"/><line x1="12" y1="21" x2="12" y2="23"/><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/><line x1="1" y1="12" x2="3" y2="12"/><line x1="21" y1="12" x2="23" y2="12"/><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/></g>`;
  }

  // Theme toggle
  const toggleTheme = document.getElementById('toggleTheme');
  const themeIcon = document.getElementById('themeIcon');
  const body = document.body;
  let isLight = false;

  function setTheme(light: boolean) {
    if (light) {
      body.classList.add('light-theme');
      if (toggleTheme) toggleTheme.classList.add('light');
      if (themeIcon) themeIcon.innerHTML = getThemeIconSVG();
    } else {
      body.classList.remove('light-theme');
      if (toggleTheme) toggleTheme.classList.remove('light');
      if (themeIcon) themeIcon.innerHTML = `<circle cx="12" cy="12" r="5"/><g id="rays"><line x1="12" y1="1" x2="12" y2="3"/><line x1="12" y1="21" x2="12" y2="23"/><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/><line x1="1" y1="12" x2="3" y2="12"/><line x1="21" y1="12" x2="23" y2="12"/><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/></g>`;
    }
  }

  // Persist theme across pages
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) {
    isLight = savedTheme === 'light';
    setTheme(isLight);
  }

  if (toggleTheme) {
    toggleTheme.addEventListener('click', () => {
      isLight = !isLight;
      setTheme(isLight);
      localStorage.setItem('theme', isLight ? 'light' : 'dark');
    });
  }

  // Auth form logic (already implemented, but ensure fade-in)
  const authForms = document.querySelectorAll('.auth-forms form');
  authForms.forEach((form, i) => {
    form.classList.add('fade-in');
    form.setAttribute('style', `animation-delay: ${0.5 + i * 0.1}s`);
  });

  // Navigation active state
  const navLinks = document.querySelectorAll('nav a');
  navLinks.forEach(link => {
    if (window.location.pathname.endsWith(link.getAttribute('href') || '')) {
      link.classList.add('active');
    }
  });
  
  // --- AUTH LOGIC ---
  // Defensive: Only run auth logic if authForm exists
  const authForm = document.getElementById('authForm') as HTMLFormElement | null;
  const mainContent = document.getElementById('main-content');
  const authSection = document.querySelector('.auth-section') as HTMLElement | null;

  // Check if user is logged in
  const { data: { session } } = await supabase.auth.getSession();

  if (authForm && authSection && mainContent) {
    if (session) {
      authSection.style.display = 'none';
      mainContent.style.display = '';
    } else {
      authSection.style.display = '';
      mainContent.style.display = 'none';
    }
  } else {
    console.warn('authForm, authSection, or mainContent element not found!');
  }

  if (loadingOverlay) loadingOverlay.style.display = 'none';

  if (authForm) {
    const authTitle = document.getElementById('authTitle') as HTMLElement | null;
    const authEmail = document.getElementById('authEmail') as HTMLInputElement | null;
    const authPassword = document.getElementById('authPassword') as HTMLInputElement | null;
    const authSubmit = document.getElementById('authSubmit') as HTMLButtonElement | null;
    const authSwitch = document.getElementById('authSwitch') as HTMLAnchorElement | null;
    const authMessage = document.getElementById('authMessage') as HTMLElement | null;
    const authGoogle = document.getElementById('authGoogle') as HTMLButtonElement | null;
    const authGithub = document.getElementById('authGithub') as HTMLButtonElement | null;
    let isLogin = true;
    const authExtra = document.getElementById('authExtra') as HTMLElement | null;
    let isReset = false;

    function setFormMode(login: boolean, reset: boolean = false) {
      isLogin = login;
      isReset = reset;
      if (reset) {
        if (authTitle) authTitle.textContent = 'Reset Password';
        if (authSubmit) authSubmit.textContent = 'Send Reset Link';
        if (authPassword) authPassword.style.display = 'none';
        if (authExtra) authExtra.innerHTML = '';
        if (authSwitch) authSwitch.textContent = 'Back to Login';
        if (authGoogle) authGoogle.style.display = 'none';
        if (authGithub) authGithub.style.display = 'none';
      } else {
        if (authTitle) authTitle.textContent = login ? 'Login' : 'Sign Up';
        if (authSubmit) authSubmit.textContent = login ? 'Login' : 'Sign Up';
        if (authPassword) authPassword.style.display = '';
        if (authExtra) authExtra.innerHTML = login ? '<a href="#" id="authForgot">Forgot password?</a>' : '';
        if (authSwitch) authSwitch.textContent = login ? "Don't have an account? Sign up" : 'Already have an account? Login';
        if (authGoogle) authGoogle.style.display = '';
        if (authGithub) authGithub.style.display = '';
      }
      if (authMessage) {
        authMessage.textContent = '';
      } else {
        console.warn('authMessage element not found!');
      }
    }

    setFormMode(true);

    // Add event listener for forgot password link
    authForm.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target && target.id === 'authForgot') {
        e.preventDefault();
        setFormMode(true, true); // Switch to reset mode
      }
    });

    if (authSwitch) {
      authSwitch.onclick = (e) => {
        e.preventDefault();
        if (isReset) {
          setFormMode(true);
        } else {
          setFormMode(!isLogin);
        }
      };
    } else {
      console.warn('authSwitch element not found!');
    }

    authForm.onsubmit = async (e) => {
      e.preventDefault();
      if (authMessage) authMessage.textContent = '';
      const email = authEmail ? authEmail.value : '';
      const password = authPassword ? authPassword.value : '';
      // Zod validation
      const loginSchema = z.object({
        email: z.string().email({ message: 'Invalid email' }),
        password: z.string().min(6, { message: 'Password must be at least 6 characters' })
      });
      const result = loginSchema.safeParse({ email, password });
      if (!result.success) {
        const msg = result.error.issues[0].message;
        if (authMessage) authMessage.textContent = DOMPurify.sanitize(msg);
        return;
      }
      if (isReset) {
        try {
          await resetPassword(email);
          if (authMessage) authMessage.textContent = 'Password reset email sent!';
          setFormMode(true);
        } catch (error: unknown) {
          if (authMessage) authMessage.textContent = DOMPurify.sanitize(error instanceof Error ? error.message : 'Reset failed');
        }
        return;
      }
      if (isLogin) {
        try {
          await signIn(email, password);
          if (authMessage) {
            authMessage.textContent = 'Signed in successfully!';
            setTimeout(() => {
              window.location.href = '/index.html';
            }, 800);
          }
        } catch (error: unknown) {
          if (authMessage) authMessage.textContent = DOMPurify.sanitize(error instanceof Error ? error.message : 'Login failed');
        }
      } else {
        try {
          await signUp(email, password);
          if (authMessage) {
            authMessage.textContent = 'Signup successful! Please check your email.';
          }
        } catch (error: Error | unknown) { 
          if (authMessage) authMessage.textContent = DOMPurify.sanitize(error instanceof Error ? error.message : 'Signup failed');
        }
      }
    };
  } else {
    // If authForm is not present, skip all auth logic
    console.info('authForm not found: skipping authentication logic.');
  }

  // Add sign out button logic
  const signOutBtn = document.getElementById('signOutBtn');
  if (signOutBtn) {
    signOutBtn.addEventListener('click', async (e) => {
      e.preventDefault(); // Prevent navigation since it's an <a>
      const { error } = await supabase.auth.signOut();
      if (!error) {
        alert('Signed out successfully!');
        // Update UI immediately after sign-out
        if (authSection && mainContent) {
          authSection.style.display = '';
          mainContent.style.display = 'none';
        }
        window.location.href = '/index.html'; // Redirect to login page
      } else {
        alert('Sign out failed: ' + error.message);
      }
    });
  }

  // Up button logic
  const upBtn = document.getElementById('upBtn');
  if (upBtn) {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 200) {
        upBtn.classList.add('show');
      } else {
        upBtn.classList.remove('show');
      }
    });
    upBtn.addEventListener('click', () => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
});