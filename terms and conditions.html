<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms and Conditions | Student Programming Hub</title>
    <meta name="description" content="Read the terms and conditions for using Student Programming Hub. Understand your rights and responsibilities as a user of our educational platform.">
    <meta name="keywords" content="terms, conditions, student programming hub, education, web development, user agreement">
    <meta name="author" content="Student Programming Hub">
    <link rel="stylesheet" href="/style.css">
    <script type="module" src="/scripts/main.ts"></script>
</head>
<body>
    <header>
        <div class="toggle-theme" id="toggleTheme" title="Toggle light/dark mode">
            <svg id="themeIcon" viewBox="0 0 24 24" aria-hidden="true" focusable="false">
                <circle cx="12" cy="12" r="5"/>
                <g id="rays">
                    <line x1="12" y1="1" x2="12" y2="3"/>
                    <line x1="12" y1="21" x2="12" y2="23"/>
                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                    <line x1="1" y1="12" x2="3" y2="12"/>
                    <line x1="21" y1="12" x2="23" y2="12"/>
                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
                </g>
            </svg>
        </div>
        <nav aria-label="Main navigation">
            <a href="/index.html">Home</a>
            <a href="/services.html">Services</a>
            <a href="/Blog.html">Blog</a>
            <a href="/about.html">About</a>
            <a href="/Privacy.html">Privacy Policy</a>
            <a href="/terms and conditions.html" aria-current="page">Terms and Conditions</a>
            <button id="signOutBtn" style="margin-left:1em;">Sign Out</button>
        </nav>
    </header>
    <main>
        <section class="card">
            <h1>Terms and Conditions</h1>
            <p>By using Student Programming Hub, you agree to use our resources for educational purposes only. Please respect our community guidelines and intellectual property. For questions, contact us.</p>
        </section>
        <div class="cta">
            <p>Need clarification? <a href="about.html">Contact our team</a></p>
        </div>
    </main>
    <footer>
        &copy; 2025 All rights reserved | Student Programming Hub
    </footer>
</body>
</html>