<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog | Student Programming Hub</title>
    <meta name="description" content="Read the latest articles, tutorials, and success stories from Student Programming Hub. Stay updated on programming, web development, and student achievements.">
    <meta name="keywords" content="blog, articles, tutorials, programming, web development, student stories, education">
    <meta name="author" content="Student Programming Hub">
    <script>
      // Prevent flash of wrong theme
      try {
        var theme = localStorage.getItem('theme');
        if (theme === 'light') {
          document.documentElement.classList.add('light-theme');
        }
      } catch(e) {}
    </script>
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="/src/styles/modules/glass-card.module.css">
    <link rel="stylesheet" href="/src/styles/modules/animations.module.css">
    <script type="module" src="/scripts/main.ts"></script>
</head>
<body>
    <div class="toggle-theme" id="toggleTheme" title="Toggle light/dark mode">
      <svg id="themeIcon" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="5"/>
        <g id="rays">
          <line x1="12" y1="1" x2="12" y2="3"/>
          <line x1="12" y1="21" x2="12" y2="23"/>
          <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
          <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
          <line x1="1" y1="12" x2="3" y2="12"/>
          <line x1="21" y1="12" x2="23" y2="12"/>
          <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
          <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
        </g>
      </svg>
    </div>
    <nav>
        <a href="/index.html">Home</a>
        <a href="/services.html">Services</a>
        <a href="/Blog.html">Blog</a>
        <a href="/about.html">About</a>
        <a href="/Privacy.html">Privacy Policy</a>
        <a href="/terms and conditions.html">Terms and Conditions</a>
        <button id="signOutBtn" style="margin-left:1em;">Sign Out</button>
    </nav>
    <div class="container">
        <section class="card glass-card fade-in">
            <h1>Blog</h1>
            <p>Welcome to our blog! Here you'll find the latest articles, tutorials, and student success stories. Stay tuned for regular updates and tips on programming and web development.</p>
            <ul>
                <li><strong>How to Start Learning HTML as a Student</strong> <span style="color:#888;">(Coming soon)</span></li>
                <li><strong>Top 5 Free Resources for Coding Beginners</strong> <span style="color:#888;">(Coming soon)</span></li>
                <li><strong>Student Success: Building Your First Website</strong> <span style="color:#888;">(Coming soon)</span></li>
            </ul>
        </section>
        <div class="cta">
            <p>Want to contribute? <a href="about.html">Contact us to share your story!</a></p>
        </div>
    </div>
    <footer>
        &copy; 2025 All rights reserved | Student Programming Hub
    </footer>
</body>
</html>