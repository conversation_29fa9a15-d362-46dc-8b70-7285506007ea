{"name": "student-programming-hub", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "cypress run", "test:e2e": "cypress run --headless", "lint": "eslint . --ext .js,.ts,.tsx", "lint:fix": "eslint . --ext .js,.ts,.tsx --fix", "spellcheck": "cspell \"**/*.{js,ts,tsx,html,md}\"", "audit": "npm audit", "audit:fix": "npm audit fix", "security:check": "npm run audit && npm run lint && npm run type-check", "security:deps": "npm outdated && npm audit", "type-check": "tsc --noEmit", "build:secure": "npm run security:check && npm run build", "snyk": "snyk test"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/dompurify": "^3.2.0", "cypress": "^14.4.0", "eslint": "^9.27.0", "eslint-plugin-security": "^3.0.1", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vite-plugin-pwa": "^0.21.1", "wait-on": "^7.2.0"}, "dependencies": {"@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.49.8", "dompurify": "^3.2.6", "zod": "^3.25.28"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}