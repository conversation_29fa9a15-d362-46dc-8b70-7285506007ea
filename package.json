{"name": "student-programming-hub", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "cypress run", "test:e2e": "cypress run --headless", "lint": "eslint . --ext .js,.ts,.tsx", "spellcheck": "cspell \"**/*.{js,ts,tsx,html,md}\"", "audit": "npm audit", "snyk": "snyk test"}, "devDependencies": {"@eslint/js": "^9.27.0", "cypress": "^14.4.0", "eslint": "^9.27.0", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "wait-on": "^7.2.0"}, "dependencies": {"@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.49.8", "dompurify": "^3.2.6", "zod": "^3.25.28"}}