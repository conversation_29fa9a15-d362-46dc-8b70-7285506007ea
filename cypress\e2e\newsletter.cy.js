/// <reference types="cypress" />
/* global cy, describe, it, beforeEach */

describe('Newsletter Signup', () => {
  beforeEach(() => {
    cy.visit('/index.html');
  });

  it('should show and validate the newsletter form', () => {    
    cy.get('#showNewsletterForm').click();
    cy.get('#newsletterForm').should('be.visible');
    cy.get('#newsletterEmail').type('invalid-email');
    cy.get('#newsletterForm').submit();
    cy.get('#newsletterMsg').should('contain', 'valid email');
  });

  it('should accept valid email and show thank you', () => {
    cy.get('#showNewsletterForm').click();
    cy.get('#newsletterEmail').type('<EMAIL>');
    cy.get('#newsletterForm').submit();
    cy.get('#newsletterMsg').should('contain', 'Thank you');
  });
});
