import { supabase } from './supabaseClient';
import { handleError } from '../src/utils/errorHandler';
import {
  authRateLimiter,
  validateEmail,
  validatePassword,
  sanitizeInput,
  secureStorage,
  secureLog,
  generateSecureToken
} from '../src/utils/security';

// Get user identifier for rate limiting (IP simulation using session storage)
function getUserIdentifier(): string {
  let identifier = secureStorage.getItem('user_session_id');
  if (!identifier) {
    identifier = generateSecureToken(16);
    secureStorage.setItem('user_session_id', identifier);
  }
  return identifier;
}

// Helper to check if user's email is confirmed
export async function isEmailConfirmed() {
  const {
    data: { user },
    error
  } = await supabase.auth.getUser();
  if (error) throw error;
  return user?.email_confirmed_at !== null;
}

export async function signUp(email: string, password: string) {
  const userId = getUserIdentifier();

  // Rate limiting check
  if (authRateLimiter.isRateLimited(userId)) {
    const remaining = authRateLimiter.getRemainingAttempts(userId);
    secureLog.warn('Sign up rate limited', { userId, remaining });
    throw new Error(`Too many attempts. Please wait before trying again. Remaining attempts: ${remaining}`);
  }

  // Input validation and sanitization
  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    throw new Error(emailValidation.error || 'Invalid email format');
  }

  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    throw new Error(passwordValidation.errors.join('. '));
  }

  if (passwordValidation.strength === 'weak') {
    secureLog.warn('Weak password attempted', { userId });
    throw new Error('Password is too weak. Please choose a stronger password.');
  }

  authRateLimiter.recordAttempt(userId);

  try {
    // Supabase will send a confirmation email by default
    const { data, error } = await supabase.auth.signUp({
      email: emailValidation.sanitized,
      password: sanitizeInput(password)
    });

    if (error) {
      secureLog.error('Sign up failed', { error: error.message, userId });
      throw error;
    }

    secureLog.info('Sign up successful', { userId, email: emailValidation.sanitized });
    return data;
  } catch (error) {
    secureLog.error('Sign up error', { error, userId });
    throw error;
  }
}

export async function signIn(email: string, password: string) {
  const userId = getUserIdentifier();

  try {
    // Rate limiting check
    if (authRateLimiter.isRateLimited(userId)) {
      const remaining = authRateLimiter.getRemainingAttempts(userId);
      secureLog.warn('Sign in rate limited', { userId, remaining });
      throw new Error(`Too many attempts. Please wait before trying again. Remaining attempts: ${remaining}`);
    }

    // Input validation and sanitization
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      throw new Error(emailValidation.error || 'Invalid email format');
    }

    authRateLimiter.recordAttempt(userId);

    const { data, error } = await supabase.auth.signInWithPassword({
      email: emailValidation.sanitized,
      password: sanitizeInput(password)
    });

    if (error) {
      secureLog.error('Sign in failed', { error: error.message, userId });
      throw error;
    }

    // Check if email is confirmed
    const confirmed = await isEmailConfirmed();
    if (!confirmed) {
      secureLog.warn('Unconfirmed email sign in attempt', { userId, email: emailValidation.sanitized });
      throw new Error('Email not confirmed. Please check your email and click the confirmation link.');
    }

    secureLog.info('Sign in successful', { userId, email: emailValidation.sanitized });
    return data;
  } catch (error) {
    const friendlyMessage = handleError(error);
    secureLog.error('Sign in error', { error, userId });
    // Don't use alert in production - this should be handled by the UI
    if (import.meta.env.DEV) {
      alert(friendlyMessage);
    }
    throw error;
  }
}

export async function signInWithProvider(provider: 'google' | 'github') {
  const userId = getUserIdentifier();

  try {
    // Rate limiting for OAuth attempts
    if (authRateLimiter.isRateLimited(userId)) {
      const remaining = authRateLimiter.getRemainingAttempts(userId);
      secureLog.warn('OAuth sign in rate limited', { userId, provider, remaining });
      throw new Error(`Too many attempts. Please wait before trying again. Remaining attempts: ${remaining}`);
    }

    authRateLimiter.recordAttempt(userId);

    // OAuth providers handle email confirmation themselves
    const result = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/index.html`
      }
    });

    secureLog.info('OAuth sign in initiated', { userId, provider });
    return result;
  } catch (error) {
    secureLog.error('OAuth sign in error', { error, userId, provider });
    throw error;
  }
}

export async function resetPassword(email: string) {
  const userId = getUserIdentifier();

  try {
    // Rate limiting for password reset
    if (authRateLimiter.isRateLimited(userId)) {
      const remaining = authRateLimiter.getRemainingAttempts(userId);
      secureLog.warn('Password reset rate limited', { userId, remaining });
      throw new Error(`Too many attempts. Please wait before trying again. Remaining attempts: ${remaining}`);
    }

    // Input validation and sanitization
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      throw new Error(emailValidation.error || 'Invalid email format');
    }

    authRateLimiter.recordAttempt(userId);

    const result = await supabase.auth.resetPasswordForEmail(emailValidation.sanitized, {
      redirectTo: `${window.location.origin}/login.html?mode=reset`
    });

    secureLog.info('Password reset requested', { userId, email: emailValidation.sanitized });
    return result;
  } catch (error) {
    secureLog.error('Password reset error', { error, userId });
    throw error;
  }
}

/**
 * Secure sign out with cleanup
 */
export async function signOut() {
  const userId = getUserIdentifier();

  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      secureLog.error('Sign out failed', { error: error.message, userId });
      throw error;
    }

    // Clear sensitive data from storage
    secureStorage.removeItem('user_session_id');
    secureStorage.removeItem('theme'); // Keep theme preference

    secureLog.info('Sign out successful', { userId });
    return { error: null };
  } catch (error) {
    secureLog.error('Sign out error', { error, userId });
    throw error;
  }
}

// NOTE: For true security, enable rate limiting on your backend (Supabase Edge Functions, API Gateway, or reverse proxy like Cloudflare/Vercel).
// Client-side rate limiting is only a user experience improvement and not a security guarantee.
// Additional security measures to implement:
// 1. Server-side rate limiting
// 2. CAPTCHA for repeated failed attempts
// 3. Account lockout policies
// 4. Audit logging
// 5. Anomaly detection
// 6. Multi-factor authentication (MFA)