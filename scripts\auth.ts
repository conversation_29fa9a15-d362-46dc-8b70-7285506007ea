import { supabase } from './supabaseClient';
import { handleError } from '../src/utils/errorHandler';

// Simple in-memory rate limiter (per session)
const RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 minute
const MAX_ATTEMPTS = 5;
let attemptTimestamps: number[] = [];

function isRateLimited() {
  const now = Date.now();
  // Remove timestamps older than window
  attemptTimestamps = attemptTimestamps.filter(ts => now - ts < RATE_LIMIT_WINDOW_MS);
  return attemptTimestamps.length >= MAX_ATTEMPTS;
}

function recordAttempt() {
  attemptTimestamps.push(Date.now());
}

// Helper to check if user's email is confirmed
export async function isEmailConfirmed() {
  const {
    data: { user },
    error
  } = await supabase.auth.getUser();
  if (error) throw error;
  return user?.email_confirmed_at !== null;
}

export async function signUp(email: string, password: string) {
  if (isRateLimited()) {
    throw new Error('Too many attempts. Please wait a minute and try again.');
  }
  recordAttempt();
  // Supabase will send a confirmation email by default
  const { data, error } = await supabase.auth.signUp({ email, password });
  if (error) throw error;
  return data;
}

export async function signIn(email: string, password: string) {
  try {
    if (isRateLimited()) {
      throw new Error('Too many attempts. Please wait a minute and try again.');
    }
    recordAttempt();
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) throw error;
    // Check if email is confirmed
    const confirmed = await isEmailConfirmed();
    if (!confirmed) {
      throw new Error('Email not confirmed');
    }
    return data;
  } catch (error) {
    const friendlyMessage = handleError(error);
    alert(friendlyMessage);
    throw error;
  }
}

export async function signInWithProvider(provider: 'google' | 'github') {
  // OAuth providers handle email confirmation themselves
  return await supabase.auth.signInWithOAuth({ provider });
}

export async function resetPassword(email: string) {
  return await supabase.auth.resetPasswordForEmail(email);
}

// NOTE: For true security, enable rate limiting on your backend (Supabase Edge Functions, API Gateway, or reverse proxy like Cloudflare/Vercel). Client-side rate limiting is only a user experience improvement and not a security guarantee.