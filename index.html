<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HTML Crash Course</title>
    <meta name="description" content="A modern educational website for students to learn programming, HTML, and web development. Find resources, community, and success stories.">
    <meta name="keywords" content="HTML, programming, education, students, web development, resources, tutorials, community, success stories">
    <meta name="author" content="Student Programming Hub">
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="/src/styles/modules/glass-card.module.css">
    <link rel="stylesheet" href="/src/styles/modules/animations.module.css">
  </head>
  <body>
    <div id="loadingOverlay" style="position:fixed;top:0;left:0;width:100vw;height:100vh;display:flex;align-items:center;justify-content:center;background:rgba(10,10,10,0.85);z-index:9999;color:#0ff;font-size:2rem;">Loading...</div>
    <header>
      <div class="toggle-theme" id="toggleTheme" title="Toggle light/dark mode">
        <svg id="themeIcon" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="5"/> 
          <g id="rays">
            <line x1="12" y1="1" x2="12" y2="3"/>
            <line x1="12" y1="21" x2="12" y2="23"/>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
            <line x1="1" y1="12" x2="3" y2="12"/>
            <line x1="21" y1="12" x2="23" y2="12"/>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
          </g>
        </svg>
      </div>
      <nav aria-label="Main navigation">
        <a href="/index.html" aria-current="page">Home</a>
        <a href="/services.html">Services</a>
        <a href="/Blog.html">Blog</a>
        <a href="/about.html">About</a>
        <a href="/Privacy.html">Privacy Policy</a>
        <a href="/terms and conditions.html">Terms and Conditions</a>
        <button id="signOutBtn" style="margin-left:1em;">Sign Out</button>
      </nav>
    </header>
    <main>
      <div class="container">
        <section class="card glass-card fade-in flex-row">
          <div class="flex-col" style="flex:2">
            <h1>Welcome To My HTML Crash Course</h1>
            <h2>Your dreams are going to come true with just HTML</h2>
            <h3>We will introduce you to HTML step by step</h3>
            <h4>By the end of the day, you will be able to build your own website from scratch</h4>
            <h5>Start today or never</h5>
            <h6>We offer a free HTML crash course for all learners, beginners or advanced. If you are a visual learner, we have you covered with video tutorials.</h6>
          </div>
          <img src="/pexels-divinetechygirl-1181298.jpg" alt="Student learning HTML on a laptop" width="400" height="266" class="img-card" style="flex:1;max-width:400px;">
        </section>
        <section class="card glass-card fade-in flex-row">
          <img src="/pexels-cottonbro-5473955.jpg" alt="Group of students learning together" width="300" height="200" class="img-card" style="flex:1;max-width:300px;">
          <div class="flex-col" style="flex:2">
            <strong>HTML Crash Course</strong>
            <p>Welcome to my crash course where we are going to learn about HTML. In this course, you'll achieve your goal to start building your own website in simple steps. No need to feel overwhelmed—I'll be there with you every step of the way. Any questions you have, I'll answer them. Also, don't forget to subscribe to my channel for more content. I'll be uploading more content soon.</p>
          </div>
        </section>
        <section class="card glass-card fade-in">
          <p>Also, my private members who have been waiting for this course for a long time now: <i>What you can do is join my other community where we have more content and more people to learn with you. Please do try reaching out to me if you have any questions or concerns.</i></p>
        </section>
        <section class="card glass-card fade-in">
          <strong>Follow us on all social media platforms:</strong>
          <ul>
            <li>Facebook</li>
            <li>Twitter</li>
            <li>YouTube</li>
            <li>GitHub</li>
            <li>Instagram</li>
            <li>LinkedIn</li>
          </ul>
        </section>
        <section class="card glass-card fade-in contact-info flex-row">
          <address style="flex:2">
            123 Main St<br>
            Braamfontein, Johannesburg, South Africa 12345<br>
            <a href="mailto:<EMAIL>"><EMAIL></a><br>
            <a href="tel:1234567890">************</a><br>
            <a href="https://www.mywebsite.com">mywebsite.com</a>
          </address>
          <img src="/pexels-luis-gomes-166706-546819.jpg" alt="Contact Student Programming Hub" width="200" height="133" class="img-card" style="flex:1;max-width:200px;">
        </section>
        <section class="testimonials glass-card fade-in">
          <div class="testimonial">
            "This course made HTML so easy to understand! I built my first website in a day."<br><b>- Aisha, Student</b>
          </div>
          <div class="testimonial">
            "The community is so supportive and the resources are top-notch. Highly recommend!"<br><b>- Daniel, Learner</b>
          </div>
        </section>
        <div class="cta glass-card fade-in">
          <span>Ready to start learning?</span>
          <button class="cta-btn" id="showNewsletterForm" type="button">Sign up for our newsletter!</button>
          <form id="newsletterForm" style="margin-top:1em;" autocomplete="off">
            <input type="email" id="newsletterEmail" placeholder="Enter your email" required style="max-width:250px;">
            <button type="submit">Subscribe</button>
            <span id="newsletterMsg" style="margin-left:1em;color:#0ff;"></span>
          </form>
        </div>
      </div>
    </main>
    <footer>
      &copy; 2025 All rights reserved | HTML Crash Course
    </footer>
    <button id="upBtn" title="Back to top" aria-label="Scroll to top">
      <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg>
    </button>
    <script type="module" src="/scripts/main.ts"></script>
  </body>
</html>