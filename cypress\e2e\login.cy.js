/// <reference types="cypress" />
/* global cy, describe, it, beforeEach */

// cypress/e2e/login.cy.js

describe('Login Page', () => {
  beforeEach(() => {
    cy.visit('/login.html');
  });

  it('should display the login form', () => {
    cy.get('form#authForm').should('be.visible');
    cy.get('input#authEmail').should('exist');
    cy.get('input#authPassword').should('exist');
    cy.get('button#authSubmit').should('contain', 'Login');
  });

  it('should show error on empty submit', () => {
    cy.get('button#authSubmit').click();
    cy.get('#authMessage').should('exist');
  });

  it('should allow switching to sign up', () => {
    cy.get('#authSwitch').click();
    cy.get('button#authSubmit').should('contain', 'Sign Up');
  });

  // Add more tests for valid/invalid login as needed
});
