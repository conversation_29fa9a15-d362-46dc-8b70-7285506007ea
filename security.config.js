// security.config.js
// Production security configuration for deployment

/**
 * Security Headers Configuration
 * Use this configuration with your web server (Nginx, Apache, Cloudflare, etc.)
 */
export const securityHeaders = {
  // Content Security Policy - Adjust based on your needs
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net",
    "img-src 'self' data: https://* blob:",
    "font-src 'self' https://fonts.gstatic.com data:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),

  // Prevent clickjacking
  'X-Frame-Options': 'DENY',

  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',

  // XSS Protection
  'X-XSS-Protection': '1; mode=block',

  // Referrer Policy
  'Referrer-Policy': 'strict-origin-when-cross-origin',

  // Permissions Policy (Feature Policy)
  'Permissions-Policy': [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
    'ambient-light-sensor=()',
    'autoplay=()',
    'battery=()',
    'display-capture=()',
    'document-domain=()',
    'encrypted-media=()',
    'fullscreen=(self)',
    'gamepad=()',
    'picture-in-picture=()'
  ].join(', '),

  // HTTP Strict Transport Security
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',

  // Cache Control for security-sensitive pages
  'Cache-Control': 'no-cache, no-store, must-revalidate, private',
  'Pragma': 'no-cache',
  'Expires': '0'
};

/**
 * Nginx Configuration Example
 * Add this to your nginx.conf or site configuration
 */
export const nginxConfig = `
# Security Headers
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Content Security Policy
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; img-src 'self' data: https://* blob:; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;" always;

# Permissions Policy
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" always;

# Hide server information
server_tokens off;

# Rate limiting
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=general:10m rate=30r/m;

# Apply rate limiting to auth endpoints
location /auth {
    limit_req zone=auth burst=3 nodelay;
}

# Apply general rate limiting
location / {
    limit_req zone=general burst=10 nodelay;
}

# Security for static files
location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header X-Content-Type-Options "nosniff" always;
}

# Block access to sensitive files
location ~ /\\. {
    deny all;
}

// eslint-disable-next-line no-useless-escape
location ~ \\.(env|log|config)$ {
    deny all;
}
`;

/**
 * Apache .htaccess Configuration Example
 */
export const apacheConfig = `
# Security Headers
Header always set X-Frame-Options "DENY"
Header always set X-Content-Type-Options "nosniff"
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

# Content Security Policy
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; img-src 'self' data: https://* blob:; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;"

# Hide server information
ServerTokens Prod
ServerSignature Off

# Block access to sensitive files
# eslint-disable-next-line no-useless-escape
<FilesMatch "\\.(env|log|config)$">
    Require all denied
</FilesMatch>

# eslint-disable-next-line no-useless-escape
<FilesMatch "^\\.">
    Require all denied
</FilesMatch>

# Security for static files
<FilesMatch "\\.(js|css|png|jpg|jpeg|gif|ico|svg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
    Header set Cache-Control "public, immutable"
</FilesMatch>
`;

/**
 * Cloudflare Workers Configuration Example
 */
export const cloudflareWorkerConfig = `
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const response = await fetch(request)
  const newResponse = new Response(response.body, response)

  // Add security headers
  newResponse.headers.set('X-Frame-Options', 'DENY')
  newResponse.headers.set('X-Content-Type-Options', 'nosniff')
  newResponse.headers.set('X-XSS-Protection', '1; mode=block')
  newResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  newResponse.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')

  // Content Security Policy
  newResponse.headers.set('Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; img-src 'self' data: https://* blob:; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;"
  )

  return newResponse
}
`;

/**
 * Environment Variables Checklist
 */
export const environmentChecklist = {
  required: [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ],
  optional: [
    'VITE_APP_ENV',
    'VITE_SENTRY_DSN',
    'VITE_ANALYTICS_ID'
  ],
  security: [
    'Never commit .env files to version control',
    'Use different keys for development and production',
    'Rotate keys regularly',
    'Monitor key usage in Supabase dashboard',
    'Enable RLS (Row Level Security) in Supabase',
    'Configure proper CORS settings'
  ]
};

/**
 * Security Checklist for Production
 */
export const productionSecurityChecklist = {
  infrastructure: [
    '✓ HTTPS enabled with valid SSL certificate',
    '✓ Security headers configured',
    '✓ Rate limiting implemented',
    '✓ DDoS protection enabled',
    '✓ Web Application Firewall (WAF) configured',
    '✓ Regular security updates applied'
  ],
  application: [
    '✓ Input validation and sanitization',
    '✓ Output encoding',
    '✓ Authentication and authorization',
    '✓ Session management',
    '✓ Error handling (no sensitive info in errors)',
    '✓ Logging and monitoring'
  ],
  database: [
    '✓ Row Level Security (RLS) enabled',
    '✓ Proper access controls',
    '✓ Regular backups',
    '✓ Encryption at rest and in transit',
    '✓ Database monitoring'
  ],
  monitoring: [
    '✓ Security event logging',
    '✓ Anomaly detection',
    '✓ Performance monitoring',
    '✓ Uptime monitoring',
    '✓ Error tracking (Sentry, etc.)'
  ]
};

/**
 * Supabase Security Configuration
 */
export const supabaseSecurityConfig = {
  rls: {
    description: 'Enable Row Level Security on all tables',
    sql: `
      -- Enable RLS on users table
      ALTER TABLE users ENABLE ROW LEVEL SECURITY;

      -- Create policy for users to only see their own data
      CREATE POLICY "Users can only see their own data" ON users
        FOR ALL USING (auth.uid() = id);
    `
  },
  auth: {
    settings: [
      'Enable email confirmation',
      'Set password requirements',
      'Configure OAuth providers securely',
      'Enable MFA when available',
      'Set session timeout',
      'Configure redirect URLs'
    ]
  }
};
