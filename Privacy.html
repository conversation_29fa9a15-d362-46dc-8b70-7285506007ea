<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Privacy Policy | Student Programming Hub</title>
  <meta name="description" content="Read the privacy policy for Student Programming Hub. Learn how we collect, use, and protect your personal information.">
  <meta name="keywords" content="privacy, policy, student programming hub, data protection, user information">
  <meta name="author" content="Student Programming Hub">
  <link rel="stylesheet" href="/style.css">
  <link rel="stylesheet" href="/src/styles/modules/glass-card.module.css">
  <link rel="stylesheet" href="/src/styles/modules/animations.module.css">
  <script type="module" src="/scripts/main.ts"></script>
</head>
<body>
  <header>
    <div class="toggle-theme" id="toggleTheme" title="Toggle light/dark mode">
      <svg id="themeIcon" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="5"/>
        <g id="rays">
          <line x1="12" y1="1" x2="12" y2="3"/>
          <line x1="12" y1="21" x2="12" y2="23"/>
          <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
          <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
          <line x1="1" y1="12" x2="3" y2="12"/>
          <line x1="21" y1="12" x2="23" y2="12"/>
          <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
          <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
        </g>
      </svg>
    </div>
    <nav aria-label="Main navigation">
      <a href="/index.html">Home</a>
      <a href="/services.html">Services</a>
      <a href="/Blog.html">Blog</a>
      <a href="/about.html">About</a>
      <a href="/Privacy.html" aria-current="page">Privacy Policy</a>
      <a href="/terms and conditions.html">Terms and Conditions</a>
      <button id="signOutBtn" style="margin-left:1em;">Sign Out</button>
    </nav>
  </header>
  <main>
    <div class="container">
      <section class="card glass-card fade-in">
        <h1>Privacy Policy</h1>
        <p>
          Your privacy is important to us. This policy explains how Student Programming Hub collects, uses, and protects your personal information.
        </p>
        <h2>Information We Collect</h2>
        <ul>
          <li>Personal information you provide (such as name, email address, etc.)</li>
          <li>Usage data (pages visited, time spent, etc.)</li>
        </ul>
        <h2>How We Use Your Information</h2>
        <ul>
          <li>To provide and improve our services</li>
          <li>To communicate with you about updates and offers</li>
          <li>To ensure the security of our platform</li>
        </ul>
        <h2>How We Protect Your Information</h2>
        <ul>
          <li>We use secure technologies and best practices</li>
          <li>We do not sell your personal information to third parties</li>
        </ul>
        <h2>Your Rights</h2>
        <ul>
          <li>You can request access to or deletion of your data at any time</li>
          <li>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a> for privacy concerns</li>
        </ul>
        <p>
          By using our site, you agree to this privacy policy. We may update this policy from time to time.
        </p>
      </section>
    </div>
  </main>
  <footer>
    &copy; 2025 All rights reserved | Student Programming Hub
  </footer>
</body>
</html>