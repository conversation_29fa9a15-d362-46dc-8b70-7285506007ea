<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services | Student Programming Hub</title>
    <meta name="description" content="Explore the services offered by Student Programming Hub: free courses, mentorship, job boards, and more for students learning programming and web development.">
    <meta name="keywords" content="services, programming, education, students, web development, mentorship, jobs, internships">
    <meta name="author" content="Student Programming Hub">
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="/src/styles/modules/glass-card.module.css">
    <link rel="stylesheet" href="/src/styles/modules/animations.module.css">
    <script type="module" src="/scripts/main.ts"></script>
  </head>
  <body>
    <header>
      <div class="toggle-theme" id="toggleTheme" title="Toggle light/dark mode">
        <svg id="themeIcon" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="5"/>
          <g id="rays">
            <line x1="12" y1="1" x2="12" y2="3"/>
            <line x1="12" y1="21" x2="12" y2="23"/>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
            <line x1="1" y1="12" x2="3" y2="12"/>
            <line x1="21" y1="12" x2="23" y2="12"/>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
          </g>
        </svg>
      </div>
      <nav aria-label="Main navigation">
        <a href="/index.html">Home</a>
        <a href="/services.html" aria-current="page">Services</a>
        <a href="/Blog.html">Blog</a>
        <a href="/about.html">About</a>
        <a href="/Privacy.html">Privacy Policy</a>
        <a href="/terms and conditions.html">Terms and Conditions</a>
        <button id="signOutBtn" style="margin-left:1em;">Sign Out</button>
      </nav>
    </header>
    <main>
      <div class="container">
        <section class="card glass-card fade-in">
          <h1>Our Services</h1>
          <ul>
            <li><b>Free HTML & Programming Courses</b> – Step-by-step guides and video tutorials for all levels.</li>
            <li><b>Mentorship</b> – Connect with experienced developers and educators for guidance.</li>
            <li><b>Job & Internship Board</b> – Find opportunities tailored for students and new graduates.</li>
            <li><b>Community Support</b> – Join our forums, Discord, and social media for Q&amp;A and networking.</li>
            <li><b>Resource Library</b> – Access curated articles, templates, and project ideas.</li>
          </ul>
        </section>
        <section class="card glass-card fade-in flex-row">
          <img src="/pexels-fauxels-3183197.jpg" alt="Mentorship and teamwork" width="300" height="200" class="img-card" style="flex:1;max-width:300px;">
          <div class="flex-col" style="flex:2">
            <h2>Why Choose Us?</h2>
            <p>We are dedicated to helping students succeed in programming and web development by providing high-quality resources, mentorship, and a supportive community.</p>
          </div>
        </section>
        <section class="card glass-card fade-in">
          <h3>Get Started</h3>
          <p>Ready to take your skills to the next level? <a href="/about.html">Learn more about us</a> or <a href="/index.html">start learning now</a>!</p>
        </section>
      </div>
    </main>
    <footer>
      &copy; 2025 All rights reserved | Student Programming Hub
    </footer>
  </body>
</html>