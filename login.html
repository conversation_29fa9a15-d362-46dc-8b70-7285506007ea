<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login | Student Programming Hub</title>
    <meta name="description" content="Login to Student Programming Hub to access programming resources and community." />
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="/src/styles/modules/glass-card.module.css">
    <link rel="stylesheet" href="/src/styles/modules/animations.module.css">
  </head>
  <body>
    <div class="toggle-theme" id="toggleTheme" title="Toggle light/dark mode">
      <svg id="themeIcon" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="5"/>
        <g id="rays">
          <line x1="12" y1="1" x2="12" y2="3"/>
          <line x1="12" y1="21" x2="12" y2="23"/>
          <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
          <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
          <line x1="1" y1="12" x2="3" y2="12"/>
          <line x1="21" y1="12" x2="23" y2="12"/>
          <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
          <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
        </g>
      </svg>
    </div>
    <section class="auth-section glass-card fade-in">
      <form class="auth-form" id="authForm" autocomplete="on">
        <h2 id="authTitle">Login</h2>
        <input type="email" id="authEmail" placeholder="Email" required />
        <input type="password" id="authPassword" placeholder="Password" required />
        <div id="authExtra"></div>
        <button type="submit" id="authSubmit">Login</button>
        <button type="button" id="authGoogle">Login with Google</button>
        <button type="button" id="authGithub">Login with GitHub</button>
        <div id="authMessage"></div>
        <a href="#" id="authSwitch">Don't have an account? Sign up</a>
      </form>
    </section>
    <script type="module" src="/scripts/main.ts"></script>
    <script type="module">
import { handleError } from '/src/utils/errorHandler.js';
const form = document.querySelector('form');
const authMessage = document.getElementById('authMessage');
if (form) {
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    try {
      // ...login logic...
    } catch (error) {
      const friendlyMessage = handleError(error);
      if (authMessage) {
        authMessage.textContent = friendlyMessage;
        authMessage.style.color = 'red';
      } else {
        alert(friendlyMessage);
      }
    }
  });
}
</script>
  </body>
</html>
