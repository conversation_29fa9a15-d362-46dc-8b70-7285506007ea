/// <reference types="cypress" />
/* global cy, describe, it, beforeEach */

describe('Signup Flow', () => {
  beforeEach(() => {
    cy.visit('/login.html');
    cy.get('#authSwitch').click(); // Switch to signup
  });

  it('should show error for invalid email', () => {
    cy.get('#authEmail').type('notanemail');
    cy.get('#authPassword').type('123456');
    cy.get('#authSubmit').click();
    cy.get('#authMessage').should('contain', 'Invalid email');
  });

  it('should show error for short password', () => {
    cy.get('#authEmail').type('<EMAIL>');
    cy.get('#authPassword').type('123');
    cy.get('#authSubmit').click();
    cy.get('#authMessage').should('contain', 'Password must be at least 6 characters');
  });

  // Add more tests for successful signup, duplicate email, etc. as needed
});
