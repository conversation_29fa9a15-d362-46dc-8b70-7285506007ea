// src/utils/security.ts
// Comprehensive security utilities for modern web applications

import DOMPurify from 'dompurify';
import { z } from 'zod';

// ============================================================================
// INPUT VALIDATION & SANITIZATION
// ============================================================================

/**
 * Comprehensive input sanitization using DOMPurify
 */
export const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  });
};

/**
 * Sanitize HTML content while preserving safe tags
 */
export const sanitizeHTML = (html: string): string => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
    ALLOWED_ATTR: ['class'],
    FORBID_SCRIPT: true,
    FORBID_TAGS: ['script', 'object', 'embed', 'link', 'style', 'img', 'svg']
  });
};

/**
 * Validate and sanitize email addresses
 */
export const validateEmail = (email: string): { isValid: boolean; sanitized: string; error?: string } => {
  const sanitized = sanitizeInput(email.trim().toLowerCase());
  const emailSchema = z.string().email({ message: 'Invalid email format' });
  const result = emailSchema.safeParse(sanitized);
  
  return {
    isValid: result.success,
    sanitized,
    error: result.success ? undefined : result.error.issues[0].message
  };
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): { 
  isValid: boolean; 
  strength: 'weak' | 'medium' | 'strong'; 
  errors: string[] 
} => {
  const errors: string[] = [];
  let score = 0;

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  } else {
    score += 1;
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  const strength = score <= 2 ? 'weak' : score <= 4 ? 'medium' : 'strong';
  
  return {
    isValid: errors.length === 0,
    strength,
    errors
  };
};

// ============================================================================
// RATE LIMITING
// ============================================================================

interface RateLimitConfig {
  windowMs: number;
  maxAttempts: number;
  blockDurationMs?: number;
}

class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  private blocked: Map<string, number> = new Map();

  constructor(private config: RateLimitConfig) {}

  isRateLimited(identifier: string): boolean {
    const now = Date.now();
    
    // Check if currently blocked
    const blockedUntil = this.blocked.get(identifier);
    if (blockedUntil && now < blockedUntil) {
      return true;
    }

    // Clean up expired attempts
    const userAttempts = this.attempts.get(identifier) || [];
    const validAttempts = userAttempts.filter(timestamp => 
      now - timestamp < this.config.windowMs
    );
    
    this.attempts.set(identifier, validAttempts);
    
    return validAttempts.length >= this.config.maxAttempts;
  }

  recordAttempt(identifier: string): void {
    const now = Date.now();
    const userAttempts = this.attempts.get(identifier) || [];
    userAttempts.push(now);
    this.attempts.set(identifier, userAttempts);

    // If exceeded limit, block for additional time
    if (userAttempts.length >= this.config.maxAttempts && this.config.blockDurationMs) {
      this.blocked.set(identifier, now + this.config.blockDurationMs);
    }
  }

  getRemainingAttempts(identifier: string): number {
    const userAttempts = this.attempts.get(identifier) || [];
    const now = Date.now();
    const validAttempts = userAttempts.filter(timestamp => 
      now - timestamp < this.config.windowMs
    );
    
    return Math.max(0, this.config.maxAttempts - validAttempts.length);
  }
}

// Pre-configured rate limiters
export const authRateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 5,
  blockDurationMs: 30 * 60 * 1000 // 30 minutes block
});

export const generalRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxAttempts: 10
});

// ============================================================================
// SECURE STORAGE
// ============================================================================

/**
 * Secure localStorage wrapper with encryption simulation
 */
export const secureStorage = {
  setItem: (key: string, value: string): void => {
    try {
      // In a real app, you'd encrypt the value here
      const timestamp = Date.now();
      const data = JSON.stringify({ value, timestamp });
      localStorage.setItem(key, data);
    } catch (error) {
      console.error('Failed to store data securely:', error);
    }
  },

  getItem: (key: string, maxAge?: number): string | null => {
    try {
      const stored = localStorage.getItem(key);
      if (!stored) return null;

      const { value, timestamp } = JSON.parse(stored);
      
      // Check if data has expired
      if (maxAge && Date.now() - timestamp > maxAge) {
        localStorage.removeItem(key);
        return null;
      }

      return value;
    } catch (error) {
      console.error('Failed to retrieve data securely:', error);
      return null;
    }
  },

  removeItem: (key: string): void => {
    localStorage.removeItem(key);
  },

  clear: (): void => {
    localStorage.clear();
  }
};

// ============================================================================
// SECURITY HEADERS & CSP
// ============================================================================

/**
 * Check if the current page has proper security headers
 */
export const checkSecurityHeaders = (): { [key: string]: boolean } => {
  const headers = {
    'X-Frame-Options': false,
    'X-Content-Type-Options': false,
    'X-XSS-Protection': false,
    'Strict-Transport-Security': false,
    'Content-Security-Policy': false
  };

  // This would typically be done server-side, but we can check some client-side indicators
  if (window.location.protocol === 'https:') {
    headers['Strict-Transport-Security'] = true;
  }

  return headers;
};

/**
 * Generate a cryptographically secure random string
 */
export const generateSecureToken = (length: number = 32): string => {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Secure comparison to prevent timing attacks
 */
export const secureCompare = (a: string, b: string): boolean => {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
};

// ============================================================================
// ENVIRONMENT VALIDATION
// ============================================================================

/**
 * Validate that all required environment variables are present
 */
export const validateEnvironment = (): { isValid: boolean; missing: string[] } => {
  const required = ['VITE_SUPABASE_URL', 'VITE_SUPABASE_ANON_KEY'];
  const missing: string[] = [];

  for (const key of required) {
    if (!import.meta.env[key]) {
      missing.push(key);
    }
  }

  return {
    isValid: missing.length === 0,
    missing
  };
};

/**
 * Check if running in development mode
 */
export const isDevelopment = (): boolean => {
  return import.meta.env.DEV || import.meta.env.MODE === 'development';
};

/**
 * Security-focused console logging that only works in development
 */
export const secureLog = {
  info: (message: string, ...args: unknown[]): void => {
    if (isDevelopment()) {
      console.info(`[SECURITY] ${message}`, ...args);
    }
  },
  
  warn: (message: string, ...args: unknown[]): void => {
    if (isDevelopment()) {
      console.warn(`[SECURITY WARNING] ${message}`, ...args);
    }
  },
  
  error: (message: string, ...args: unknown[]): void => {
    if (isDevelopment()) {
      console.error(`[SECURITY ERROR] ${message}`, ...args);
    }
  }
};
