export const authErrorMap: Record<string, string> = {
  'Invalid login credentials': 'Please check your email/password',
  'Email not confirmed': 'Verify your email first',
  // Add all Supabase error codes as needed
};

export const handleError = (error: unknown) => {
  const message = error instanceof Error ? error.message : 'Unknown error';
  // Use Map.has() to avoid security/detect-object-injection
  const errorMap = new Map(Object.entries(authErrorMap));
  return errorMap.get(message) || 'Operation failed. Please try again.';
};
