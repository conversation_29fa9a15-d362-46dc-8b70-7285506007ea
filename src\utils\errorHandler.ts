export const authErrorMap: Record<string, string> = {
  'Invalid login credentials': 'Please check your email/password',
  'Email not confirmed': 'Verify your email first',
  // Add all Supabase error codes as needed
};

export const handleError = (error: unknown) => {
  const message = error instanceof Error ? error.message : 'Unknown error';
  return authErrorMap[message] || 'Operation failed. Please try again.';
};
