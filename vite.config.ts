// vite.config.ts
import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    headers: {
      // Content Security Policy: adjust sources as needed for your app
      'Content-Security-Policy': [
        "default-src 'self';",
        "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com;",
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net;",
        "img-src 'self' data: https://*;",
        "font-src 'self' https://fonts.gstatic.com;",
        "connect-src 'self' https://*.supabase.co;",
        "frame-src 'none';",
        "object-src 'none';"
      ].join(' ')
    }
  }
});
