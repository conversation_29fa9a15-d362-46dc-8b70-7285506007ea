# Node modules
node_modules/
# Environment variables
.env
.env.*
# OS files
.DS_Store
Thumbs.db
# VS Code settings
.vscode/
# Build output
dist/
build/
# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
# Cypress and test output
cypress/screenshots/
cypress/videos/
# Sensitive files
*.pem
*.key
*.crt
*.p12
*.pfx
# Misc
.idea/
*.sublime-workspace
*.sublime-project
# Ignore PDF and other docs
*.pdf
*.docx
*.doc
*.pptx
*.xlsx
# Ignore backup files
*~
# Ignore system files
Icon?
# Ignore Mac system files
.AppleDouble
.LSOverride
# Ignore Windows system files
Desktop.ini
$RECYCLE.BIN/
# Ignore temporary files
*.tmp
# Ignore test coverage
coverage/
# Ignore environment and secret files
.env.local
.env.development.local
.env.test.local
.env.production.local
# Ignore private keys and sensitive data
private.key
private.pem
id_rsa
id_rsa.pub
# Ignore VS Code workspace storage
.vscode/
