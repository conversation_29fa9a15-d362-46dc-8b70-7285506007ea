# Production-ready Dockerfile for Vite + TypeScript static site
FROM node:20.11.1-slim AS build
RUN apk update && apk upgrade --no-cache && \
    adduser -D appuser
WORKDIR /app
USER appuser

# Install dependencies first (better cache)
COPY package*.json ./
COPY tsconfig.json ./
COPY vite.config.ts ./
RUN npm install --frozen-lockfile || npm install

# Copy only necessary files for build
COPY public ./public
COPY src ./src
COPY scripts ./scripts
# If you use environment variables at build time, copy only the needed .env file
# COPY .env.production .env

RUN npm run build
FROM nginx:stable-alpine
RUN adduser -D apprunner && \
    chown -R apprunner:apprunner /usr/share/nginx/html
COPY --from=build /app/dist /usr/share/nginx/html
USER apprunner
FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html

# Optional: Healthcheck for Cloud Run
HEALTHCHECK --interval=30s --timeout=3s CMD wget -qO- http://localhost:80/ || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
